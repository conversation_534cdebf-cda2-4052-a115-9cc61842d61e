import { motion } from 'framer-motion';

const FloatingElement = ({ 
  children, 
  duration = 3,
  delay = 0,
  amplitude = 20,
  className = ''
}) => {
  const floatingVariants = {
    animate: {
      y: [-amplitude, amplitude, -amplitude],
      transition: {
        duration,
        delay,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      variants={floatingVariants}
      animate="animate"
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default FloatingElement;
