import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

const StaggerContainer = ({ 
  children, 
  stagger = 0.1,
  className = '',
  once = true,
  threshold = 0.1,
  direction = 'up'
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { 
    once, 
    threshold,
    margin: "-50px 0px -50px 0px"
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: stagger,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    up: {
      hidden: { opacity: 0, y: 50 },
      visible: { 
        opacity: 1, 
        y: 0,
        transition: {
          duration: 0.6,
          ease: [0.25, 0.25, 0.25, 0.75]
        }
      }
    },
    down: {
      hidden: { opacity: 0, y: -50 },
      visible: { 
        opacity: 1, 
        y: 0,
        transition: {
          duration: 0.6,
          ease: [0.25, 0.25, 0.25, 0.75]
        }
      }
    },
    left: {
      hidden: { opacity: 0, x: -50 },
      visible: { 
        opacity: 1, 
        x: 0,
        transition: {
          duration: 0.6,
          ease: [0.25, 0.25, 0.25, 0.75]
        }
      }
    },
    right: {
      hidden: { opacity: 0, x: 50 },
      visible: { 
        opacity: 1, 
        x: 0,
        transition: {
          duration: 0.6,
          ease: [0.25, 0.25, 0.25, 0.75]
        }
      }
    },
    scale: {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { 
        opacity: 1, 
        scale: 1,
        transition: {
          duration: 0.6,
          ease: [0.25, 0.25, 0.25, 0.75]
        }
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={containerVariants}
      className={className}
    >
      {Array.isArray(children) 
        ? children.map((child, index) => (
            <motion.div
              key={index}
              variants={itemVariants[direction]}
            >
              {child}
            </motion.div>
          ))
        : <motion.div variants={itemVariants[direction]}>{children}</motion.div>
      }
    </motion.div>
  );
};

export default StaggerContainer;
