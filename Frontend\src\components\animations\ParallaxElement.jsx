import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef } from 'react';

const ParallaxElement = ({ 
  children, 
  speed = 0.5, 
  className = '',
  direction = 'vertical' 
}) => {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, speed * 100]);
  const x = useTransform(scrollYProgress, [0, 1], [0, direction === 'horizontal' ? speed * 100 : 0]);

  return (
    <motion.div
      ref={ref}
      style={{ 
        y: direction === 'vertical' ? y : 0,
        x: direction === 'horizontal' ? x : 0
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default ParallaxElement;
