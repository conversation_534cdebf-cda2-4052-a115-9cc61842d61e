import React from 'react';
import { motion } from 'framer-motion';
import ScrollReveal from './ScrollReveal';
import StaggerContainer from './StaggerContainer';

const AnimationTest = () => {
  return (
    <div style={{ padding: '50px', minHeight: '200vh' }}>
      <h1>Animation Test</h1>
      
      <ScrollReveal direction="up" delay={0.2}>
        <div style={{ 
          background: '#f0f0f0', 
          padding: '20px', 
          margin: '20px 0',
          borderRadius: '8px'
        }}>
          <h2>Scroll Reveal Test - Up</h2>
          <p>This should animate from bottom to top when scrolled into view.</p>
        </div>
      </ScrollReveal>

      <ScrollReveal direction="left" delay={0.3}>
        <div style={{ 
          background: '#e0e0e0', 
          padding: '20px', 
          margin: '20px 0',
          borderRadius: '8px'
        }}>
          <h2>Scroll Reveal Test - Left</h2>
          <p>This should animate from right to left when scrolled into view.</p>
        </div>
      </ScrollReveal>

      <StaggerContainer stagger={0.2} direction="scale">
        <div style={{ 
          background: '#d0d0d0', 
          padding: '20px', 
          margin: '10px 0',
          borderRadius: '8px'
        }}>
          <h3>Stagger Item 1</h3>
        </div>
        <div style={{ 
          background: '#c0c0c0', 
          padding: '20px', 
          margin: '10px 0',
          borderRadius: '8px'
        }}>
          <h3>Stagger Item 2</h3>
        </div>
        <div style={{ 
          background: '#b0b0b0', 
          padding: '20px', 
          margin: '10px 0',
          borderRadius: '8px'
        }}>
          <h3>Stagger Item 3</h3>
        </div>
      </StaggerContainer>

      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        style={{ 
          background: '#a0a0a0', 
          padding: '20px', 
          margin: '20px 0',
          borderRadius: '8px'
        }}
      >
        <h2>Direct Framer Motion Test</h2>
        <p>This should animate immediately on load.</p>
      </motion.div>
    </div>
  );
};

export default AnimationTest;
