import { motion } from "framer-motion";

const PulsingButton = ({
  children,
  className = "",
  pulseScale = 1.02,
  duration = 3,
  as = "button",
  ...props
}) => {
  const pulseVariants = {
    pulse: {
      scale: [1, pulseScale, 1],
      transition: {
        duration,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  const hoverVariants = {
    hover: {
      scale: 1.05,
      y: -2,
      transition: {
        duration: 0.2,
        ease: "easeOut",
      },
    },
    tap: {
      scale: 0.98,
      y: 0,
      transition: {
        duration: 0.1,
        ease: "easeOut",
      },
    },
  };

  const MotionComponent = motion[as] || motion.div;

  return (
    <MotionComponent
      variants={pulseVariants}
      animate="pulse"
      whileHover="hover"
      whileTap="tap"
      className={className}
      {...props}
    >
      {children}
    </MotionComponent>
  );
};

export default PulsingButton;
